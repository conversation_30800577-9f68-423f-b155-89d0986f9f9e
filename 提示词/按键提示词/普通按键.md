# 普通按键

## 按键样式

**默认按键:**

- 1.按键样式:
  - 1.按键名称: secondary -> (style_secondary)
  - 2.按键形状: 长方形 -> (style_secondary)
  - 3.按键高度: 50px -> (style_secondary)
  - 4.按键宽度: 200px -> (style_secondary)
  - 5.按键圆角: 5px -> (style_secondary)
  - 6.按键底色: #f1f1f1 -> (style_secondary)
  - 6.展示方式: 弹性布局 -> (style_secondary)
  - 7.弹性方式: 水平居中 -> (style_secondary)
  - 8.对齐方式: 垂直居中 -> (style_secondary)
  - 9.鼠标指针样式: 手型 -> (style_secondary)
- 2.文字样式:
  - 1.默认文本: 按键 -> (style_secondary)
  - 2.字体大小: 20px -> (style_secondary)
  - 3.字体颜色: #242424 -> (style_secondary)
  - 4.字体对齐: 居中 -> (style_secondary)
  - 5.字体行高: 1.5 -> (style_secondary)
- 3.互动样式:
  - 1.鼠标悬停: -> (event_secondary)
    - 1.按键底色: #e4e4e4 -> (style_secondary)
  - 2.鼠标点击(切换模式): -> (event_secondary)
    - 1.按键初始状态底色: #f1f1f1 -> (style_secondary)
    - 2.按键激活状态底色: #929292 -> （style_secondary）

**按键组件:**

- 1.创建普通按键组件:
  - 3.创建按键组件 -> (SecondaryButton.tsx)
  - 4.导出按键组件 -> (index.ts)
