# 一二级容器

## 一级容器

**1.主容器布局:**

- 1.全视窗高度: 100% -> (main_container.tsx)
- 2.全视窗宽度: 100% -> (main_container.tsx)
- 3.背景颜色: #242424 -> (main_container.tsx)
- 4.展示方式: 弹性布局 -> (main_container.tsx)
- 5.主轴方向: 水平 -> (main_container.tsx)
- 6.主轴对齐: 左对齐 -> (main_container.tsx)
- 6.溢出处理: 隐藏 -> (main_container.tsx)
- 7.滚动设置: 禁止 -> (main_container.tsx)

## 二级容器

**1.二级容器布局1:**

- 1.容器形状: 正方形 -> (componet1.tsx)
- 2.视窗高度: 全视窗高度95% -> (componet1.tsx)
- 3.视窗宽度: 全视窗高95% -> (componet1.tsx)
- 4.背景颜色: #6d6d6d -> (componet1.tsx)
- 5.展示方式: 弹性布局 -> (componet1.tsx)
- 6.弹性方向: 垂直居中 -> (componet1.tsx)
- 7.对齐方式: 水平居中 -> (componet1.tsx)
- 8.溢出处理: 隐藏 -> (componet1.tsx)

**2.间隔:**

- 1.间隔宽度: 全视窗宽度1%

**3.二级容器布局2:**

- 1.容器形状: 长方形 -> (componet2.tsx)
- 2.视窗高度: 全视窗高度95% -> (componet2.tsx)
- 3.视窗宽度: 全视窗宽度20% -> (componet2.tsx)
- 4.背景颜色: #6d6d6d -> (componet2.tsx)
- 5.展示方式: 弹性布局 -> (componet2.tsx)
- 6.弹性方向: 垂直居中 -> (componet2.tsx)
- 7.溢出处理: 隐藏 -> (componet2.tsx)

**4.二级容器布局3:**

- 1.容器形状: 长方形 -> (componet3.tsx)
- 2.视窗高度: 全视窗高度95% -> (componet3.tsx)
- 3.视窗宽度: 全视窗宽度20% -> (componet3.tsx)
- 4.背景颜色: #b6b6b6 -> (componet3.tsx)
- 5.展示方式: 弹性布局 -> (componet3.tsx)
- 6.弹性方向: 垂直居中 -> (componet3.tsx)
- 7.溢出处理: 隐藏 -> (componet3.tsx)

**5.二级容器布局4:**

- 1.容器形状: 长方形 -> (componet4.tsx)
- 2.视窗高度: 全视窗高度的5% -> (componet4.tsx)
- 3.视窗宽度: 全视窗宽度的20% -> (componet4.tsx)
- 4.背景颜色: 无 -> (componet4.tsx)
- 5.展示方式: 弹性布局 -> (componet4.tsx)
- 6.弹性方向: 水平居中 -> (componet4.tsx)
- 7.溢出方式: 隐藏 -> (componet4.tsx)
- 8.容器位置: 绝对位置 -> (componet4.tsx)
- 9.顶部对齐: 与‘componet2’容器顶部对齐，间距为0 -> (componet4.tsx)
- 10.左部对齐: 与‘componet2’容器左部对齐，间距为0 -> (componet4.tsx)

**6.按键调用:**

- 1.按键调用(模式按键) -> (componet4.tsx)
  - 1.调用按键'SecondaryButton'
    - 1.配置参数:
      - 1.键高:占'componet4'容器高的100%
      - 2.键宽:占'componet4'容器宽的50%
    - 2.文本:
      - 1.键内文本: 模式
      - 2.文本大小: 尺寸自适应
- 2.按键调用(业务按键) -> (componet4.tsx)
  - 1.调用按键‘SecondaryButton’
    - 1.配置参数:
      - 1.键高:占‘component4’容器高的100%
      - 2.键宽:占‘component4’容器宽的50%
    - 2.文本:
      - 1.键内文本: 业务
      - 2.文本大小: 尺寸自适应

**7.按键交互:**

- 1.点击‘模式按键’切换至容器‘componet2.tsx’
- 2.点击‘业务按键’切换至容器'componet3.tsx'
- 3.默认状态:为‘模式按键’为激活状态，默认显示‘componet.tsx’容器
